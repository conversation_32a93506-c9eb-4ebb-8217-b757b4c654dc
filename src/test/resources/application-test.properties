# Test Application Configuration
spring.application.name=LedgerBased-Test

# Test Database Configuration (H2 in-memory for testing)
spring.datasource.url=jdbc:h2:mem:testdb;DB_CLOSE_DELAY=-1;DB_CLOSE_ON_EXIT=FALSE
spring.datasource.username=sa
spring.datasource.password=
spring.datasource.driver-class-name=org.h2.Driver

# Disable Flyway for tests (we'll use schema.sql instead)
spring.flyway.enabled=false

# JPA/Hibernate Configuration for tests
spring.jpa.hibernate.ddl-auto=create-drop
spring.jpa.show-sql=true
spring.jpa.properties.hibernate.format_sql=true

# Security Configuration for tests
spring.security.user.name=testuser
spring.security.user.password=testpass
spring.security.user.roles=ADMIN

# Logging Configuration for tests
logging.level.org.example.ledgerbased=DEBUG
logging.level.org.springframework.jdbc=DEBUG
logging.level.org.springframework.transaction=DEBUG
logging.level.org.springframework.security=DEBUG

# Disable actuator endpoints in tests
management.endpoints.enabled-by-default=false
