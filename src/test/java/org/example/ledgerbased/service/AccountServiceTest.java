package org.example.ledgerbased.service;

import org.example.ledgerbased.exception.AccountException;
import org.example.ledgerbased.model.Account;
import org.example.ledgerbased.model.AccountType;
import org.example.ledgerbased.repository.AccountRepository;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.math.BigDecimal;
import java.util.Optional;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;

/**
 * 账户服务测试类
 * 使用Mockito框架对AccountService进行单元测试
 * 测试账户创建、查询、更新和删除等核心功能
 */
@ExtendWith(MockitoExtension.class)
class AccountServiceTest {

    /** 模拟的账户仓库对象 */
    @Mock
    private AccountRepository accountRepository;

    /** 被测试的账户服务对象，自动注入模拟依赖 */
    @InjectMocks
    private AccountService accountService;

    /** 测试用的账户对象 */
    private Account testAccount;

    /**
     * 测试前的初始化方法
     * 创建测试用的账户对象
     */
    @BeforeEach
    void setUp() {
        testAccount = new Account("ACC-001", "Test Account", AccountType.ASSET, 1L, BigDecimal.valueOf(1000), 1L);
        testAccount.setId(1L);
    }

    /**
     * 测试成功创建账户的场景
     * 验证账户编号不重复时能正常创建账户
     */
    @Test
    void createAccount_Success() {
        // Given - 准备测试数据
        when(accountRepository.existsByAccountNumber(anyString())).thenReturn(false);
        when(accountRepository.save(any(Account.class))).thenReturn(testAccount);

        // When - 执行被测试的方法
        Account result = accountService.createAccount("ACC-001", "Test Account", AccountType.ASSET, 1L, BigDecimal.valueOf(1000), 1L);

        // Then - 验证结果
        assertNotNull(result);
        assertEquals("ACC-001", result.getAccountNumber());
        assertEquals("Test Account", result.getAccountName());
        assertEquals(AccountType.ASSET, result.getAccountType());
        verify(accountRepository).save(any(Account.class));
    }

    @Test
    void createAccount_DuplicateAccountNumber_ThrowsException() {
        // Given
        when(accountRepository.existsByAccountNumber("ACC-001")).thenReturn(true);

        // When & Then
        assertThrows(AccountException.DuplicateAccountNumberException.class, () -> {
            accountService.createAccount("ACC-001", "Test Account", AccountType.ASSET, 1L, BigDecimal.valueOf(1000), 1L);
        });

        verify(accountRepository, never()).save(any(Account.class));
    }

    @Test
    void getAccountById_Success() {
        // Given
        when(accountRepository.findById(1L)).thenReturn(Optional.of(testAccount));

        // When
        Account result = accountService.getAccountById(1L);

        // Then
        assertNotNull(result);
        assertEquals(testAccount.getId(), result.getId());
        assertEquals(testAccount.getAccountNumber(), result.getAccountNumber());
    }

    @Test
    void getAccountById_NotFound_ThrowsException() {
        // Given
        when(accountRepository.findById(1L)).thenReturn(Optional.empty());

        // When & Then
        assertThrows(AccountException.AccountNotFoundException.class, () -> {
            accountService.getAccountById(1L);
        });
    }

    @Test
    void getAccountByNumber_Success() {
        // Given
        when(accountRepository.findByAccountNumber("ACC-001")).thenReturn(Optional.of(testAccount));

        // When
        Account result = accountService.getAccountByNumber("ACC-001");

        // Then
        assertNotNull(result);
        assertEquals("ACC-001", result.getAccountNumber());
    }

    @Test
    void getAccountByNumber_NotFound_ThrowsException() {
        // Given
        when(accountRepository.findByAccountNumber("ACC-001")).thenReturn(Optional.empty());

        // When & Then
        assertThrows(AccountException.AccountNotFoundException.class, () -> {
            accountService.getAccountByNumber("ACC-001");
        });
    }

    @Test
    void deactivateAccount_Success() {
        // Given
        when(accountRepository.findById(1L)).thenReturn(Optional.of(testAccount));
        when(accountRepository.countSubAccounts(1L)).thenReturn(0L);
        when(accountRepository.save(any(Account.class))).thenReturn(testAccount);

        // When
        accountService.deactivateAccount(1L);

        // Then
        verify(accountRepository).save(any(Account.class));
    }

    @Test
    void deactivateAccount_WithSubAccounts_ThrowsException() {
        // Given
        when(accountRepository.findById(1L)).thenReturn(Optional.of(testAccount));
        when(accountRepository.countSubAccounts(1L)).thenReturn(2L);

        // When & Then
        assertThrows(AccountException.class, () -> {
            accountService.deactivateAccount(1L);
        });

        verify(accountRepository, never()).save(any(Account.class));
    }
}
