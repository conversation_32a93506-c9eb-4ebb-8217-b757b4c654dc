# Application Configuration
spring.application.name=LedgerBased

# Database Configuration
spring.datasource.url=******************************************
spring.datasource.username=ledger_user
spring.datasource.password=ledger_password
spring.datasource.driver-class-name=org.postgresql.Driver

# Connection Pool Configuration
spring.datasource.hikari.maximum-pool-size=20
spring.datasource.hikari.minimum-idle=5
spring.datasource.hikari.connection-timeout=30000
spring.datasource.hikari.idle-timeout=600000
spring.datasource.hikari.max-lifetime=1800000

# Flyway Configuration
spring.flyway.enabled=true
spring.flyway.locations=classpath:db/migration
spring.flyway.baseline-on-migrate=true

# Transaction Configuration
spring.transaction.default-timeout=30

# Security Configuration
spring.security.user.name=admin
spring.security.user.password=admin123
spring.security.user.roles=ADMIN

# Actuator Configuration
management.endpoints.web.exposure.include=health,info,metrics
management.endpoint.health.show-details=when-authorized

# Logging Configuration
logging.level.org.example.ledgerbased=DEBUG
logging.level.org.springframework.jdbc=DEBUG
logging.level.org.springframework.transaction=DEBUG

# JSON Configuration
spring.jackson.serialization.write-dates-as-timestamps=false
spring.jackson.time-zone=UTC
