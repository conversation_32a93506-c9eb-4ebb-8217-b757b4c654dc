package org.example.ledgerbased.config;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.config.annotation.web.configuration.EnableWebSecurity;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.security.web.SecurityFilterChain;

/**
 * Spring Security安全配置类
 * 配置系统的认证和授权策略，确保财务系统的安全性
 * 当前使用HTTP Basic认证，生产环境建议使用JWT或OAuth2
 */
@Configuration
@EnableWebSecurity
public class SecurityConfig {

    /**
     * 配置安全过滤器链
     * 定义哪些URL需要认证，哪些可以公开访问
     *
     * @param http HTTP安全配置对象
     * @return 配置好的安全过滤器链
     * @throws Exception 配置异常
     */
    @Bean
    public SecurityFilterChain filterChain(HttpSecurity http) throws Exception {
        http
            .csrf(csrf -> csrf.disable()) // 禁用CSRF，适用于API端点
            .authorizeHttpRequests(authz -> authz
                .requestMatchers("/actuator/health").permitAll() // 健康检查端点允许匿名访问
                .requestMatchers("/api/**").authenticated() // API端点需要认证
                .anyRequest().authenticated() // 其他所有请求都需要认证
            )
            .httpBasic(httpBasic -> {
                // 启用HTTP Basic认证，简单易用
                // 生产环境建议考虑JWT或OAuth2
            });

        return http.build();
    }

    /**
     * 配置密码编码器
     * 使用BCrypt算法对密码进行加密存储
     *
     * @return BCrypt密码编码器实例
     */
    @Bean
    public PasswordEncoder passwordEncoder() {
        return new BCryptPasswordEncoder();
    }
}
