package org.example.ledgerbased.config;

import org.springframework.context.annotation.Configuration;
import org.springframework.data.jdbc.repository.config.EnableJdbcRepositories;
import org.springframework.transaction.annotation.EnableTransactionManagement;

/**
 * 数据库配置类
 *
 * 配置Spring Data JDBC和事务管理，确保财务数据的ACID特性
 * 主要功能：
 * - 启用JDBC仓库：自动扫描并创建Repository接口的实现
 * - 启用事务管理：确保数据操作的原子性、一致性、隔离性和持久性
 * - 集中管理数据库相关配置
 *
 * 财务系统对数据一致性要求极高，所有涉及账户余额变更的操作
 * 都必须在事务中执行，确保复式记账的借贷平衡原则
 */
@Configuration
@EnableJdbcRepositories(basePackages = "org.example.ledgerbased.repository")
@EnableTransactionManagement
public class DatabaseConfig {

    /**
     * Spring Data JDBC配置由自动配置处理
     *
     * 此类作为自定义数据库配置的中心位置，可以在此添加：
     * - 自定义数据源配置
     * - 连接池配置
     * - 事务管理器配置
     * - 数据库方言配置
     * - 审计配置等
     *
     * 当前使用默认配置，通过application.properties文件进行参数调整
     */
}
