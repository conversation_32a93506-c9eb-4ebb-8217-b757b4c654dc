package org.example.ledgerbased.exception;

/**
 * 交易相关异常类
 *
 * 处理交易处理过程中的各种业务异常情况
 * 包括交易平衡验证、状态检查、查找失败等场景
 * 确保交易处理的严格性和数据完整性
 */
public class TransactionException extends LedgerException {

    /**
     * 构造函数，使用默认交易错误代码
     *
     * @param message 错误消息
     */
    public TransactionException(String message) {
        super(message, "TRANSACTION_ERROR");
    }

    /**
     * 构造函数，包含原因异常
     *
     * @param message 错误消息
     * @param cause 原因异常
     */
    public TransactionException(String message, Throwable cause) {
        super(message, "TRANSACTION_ERROR", cause);
    }

    /**
     * 交易不平衡异常
     *
     * 当交易的借方总额不等于贷方总额时抛出
     * 这是复式记账的基本要求，必须严格执行
     */
    public static class UnbalancedTransactionException extends TransactionException {
        /**
         * 构造函数
         *
         * @param message 具体的不平衡信息
         */
        public UnbalancedTransactionException(String message) {
            super("Transaction is not balanced: " + message);
        }
    }

    /**
     * 无效交易状态异常
     *
     * 当尝试对交易执行不符合其当前状态的操作时抛出
     * 如尝试修改已过账的交易、审批已审批的交易等
     */
    public static class InvalidTransactionStatusException extends TransactionException {
        /**
         * 构造函数
         *
         * @param message 状态错误信息
         */
        public InvalidTransactionStatusException(String message) {
            super("Invalid transaction status: " + message);
        }
    }

    /**
     * 交易未找到异常
     *
     * 当根据交易编号或ID查找交易失败时抛出
     */
    public static class TransactionNotFoundException extends TransactionException {
        /**
         * 根据交易编号构造异常
         *
         * @param transactionNumber 交易编号
         */
        public TransactionNotFoundException(String transactionNumber) {
            super("Transaction not found: " + transactionNumber);
        }

        /**
         * 根据交易ID构造异常
         *
         * @param transactionId 交易ID
         */
        public TransactionNotFoundException(Long transactionId) {
            super("Transaction not found with ID: " + transactionId);
        }
    }

    /**
     * 重复交易编号异常
     *
     * 当尝试创建具有重复编号的交易时抛出
     * 交易编号必须在系统中保持唯一性
     */
    public static class DuplicateTransactionNumberException extends TransactionException {
        /**
         * 构造函数
         *
         * @param transactionNumber 重复的交易编号
         */
        public DuplicateTransactionNumberException(String transactionNumber) {
            super("Transaction number already exists: " + transactionNumber);
        }
    }
}
