package org.example.ledgerbased.exception;

/**
 * 账户相关异常类
 *
 * 处理账户管理过程中的各种业务异常情况
 * 包括账户查找、余额验证、层级关系、状态检查等场景
 * 确保账户操作的正确性和数据完整性
 */
public class AccountException extends LedgerException {

    /**
     * 构造函数，使用默认账户错误代码
     *
     * @param message 错误消息
     */
    public AccountException(String message) {
        super(message, "ACCOUNT_ERROR");
    }

    /**
     * 构造函数，包含原因异常
     *
     * @param message 错误消息
     * @param cause 原因异常
     */
    public AccountException(String message, Throwable cause) {
        super(message, "ACCOUNT_ERROR", cause);
    }

    /**
     * 账户未找到异常
     *
     * 当根据账户编号或ID查找账户失败时抛出
     */
    public static class AccountNotFoundException extends AccountException {
        /**
         * 根据账户编号构造异常
         *
         * @param accountNumber 账户编号
         */
        public AccountNotFoundException(String accountNumber) {
            super("Account not found: " + accountNumber);
        }

        /**
         * 根据账户ID构造异常
         *
         * @param accountId 账户ID
         */
        public AccountNotFoundException(Long accountId) {
            super("Account not found with ID: " + accountId);
        }
    }

    /**
     * 重复账户编号异常
     *
     * 当尝试创建具有重复编号的账户时抛出
     * 账户编号必须在系统中保持唯一性
     */
    public static class DuplicateAccountNumberException extends AccountException {
        /**
         * 构造函数
         *
         * @param accountNumber 重复的账户编号
         */
        public DuplicateAccountNumberException(String accountNumber) {
            super("Account number already exists: " + accountNumber);
        }
    }

    /**
     * 账户未激活异常
     *
     * 当尝试对未激活的账户执行操作时抛出
     * 确保只有激活的账户才能参与交易
     */
    public static class InactiveAccountException extends AccountException {
        /**
         * 构造函数
         *
         * @param accountNumber 未激活的账户编号
         */
        public InactiveAccountException(String accountNumber) {
            super("Account is inactive: " + accountNumber);
        }
    }

    /**
     * 余额不足异常
     *
     * 当账户余额不足以支持某项操作时抛出
     * 主要用于防止资产账户出现负余额等不合理情况
     */
    public static class InsufficientBalanceException extends AccountException {
        /**
         * 构造函数
         *
         * @param accountNumber 账户编号
         * @param currentBalance 当前余额
         * @param requiredAmount 所需金额
         */
        public InsufficientBalanceException(String accountNumber, java.math.BigDecimal currentBalance, java.math.BigDecimal requiredAmount) {
            super(String.format("Insufficient balance in account %s. Current: %s, Required: %s",
                  accountNumber, currentBalance, requiredAmount));
        }
    }

    /**
     * 无效账户层级异常
     *
     * 当账户层级关系不正确时抛出
     * 如循环引用、层级过深等情况
     */
    public static class InvalidAccountHierarchyException extends AccountException {
        /**
         * 构造函数
         *
         * @param message 层级错误的具体信息
         */
        public InvalidAccountHierarchyException(String message) {
            super("Invalid account hierarchy: " + message);
        }
    }
}
