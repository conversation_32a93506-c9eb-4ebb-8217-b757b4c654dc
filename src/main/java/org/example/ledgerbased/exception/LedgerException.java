package org.example.ledgerbased.exception;

/**
 * 账本系统基础异常类
 * 所有业务异常的父类，提供错误代码和消息的统一处理
 * 用于区分不同类型的业务错误，便于客户端处理
 */
public class LedgerException extends RuntimeException {

    /** 错误代码，用于标识具体的错误类型 */
    private final String errorCode;

    /**
     * 构造函数，使用默认错误代码
     *
     * @param message 错误消息
     */
    public LedgerException(String message) {
        super(message);
        this.errorCode = "LEDGER_ERROR";
    }

    /**
     * 构造函数，指定错误代码
     *
     * @param message 错误消息
     * @param errorCode 错误代码
     */
    public LedgerException(String message, String errorCode) {
        super(message);
        this.errorCode = errorCode;
    }

    /**
     * 构造函数，包含原因异常，使用默认错误代码
     *
     * @param message 错误消息
     * @param cause 原因异常
     */
    public LedgerException(String message, Throwable cause) {
        super(message, cause);
        this.errorCode = "LEDGER_ERROR";
    }

    /**
     * 构造函数，包含原因异常和错误代码
     *
     * @param message 错误消息
     * @param errorCode 错误代码
     * @param cause 原因异常
     */
    public LedgerException(String message, String errorCode, Throwable cause) {
        super(message, cause);
        this.errorCode = errorCode;
    }

    /**
     * 获取错误代码
     *
     * @return 错误代码字符串
     */
    public String getErrorCode() {
        return errorCode;
    }
}
