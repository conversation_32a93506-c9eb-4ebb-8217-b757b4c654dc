package org.example.ledgerbased.controller;

import org.example.ledgerbased.dto.CreateTransactionRequest;
import org.example.ledgerbased.dto.TransactionEntryRequest;
import org.example.ledgerbased.model.Transaction;
import org.example.ledgerbased.model.TransactionEntry;
import org.example.ledgerbased.model.TransactionStatus;
import org.example.ledgerbased.service.TransactionService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;
import java.time.LocalDate;
import java.util.List;

/**
 * 交易管理控制器
 * 提供交易相关的RESTful API接口，实现完整的交易生命周期管理
 * 支持交易的创建、查询、审批、过账和冲销等操作
 * 严格遵循复式记账原理，确保每笔交易的借贷平衡
 */
@RestController
@RequestMapping("/api/transactions")
public class TransactionController {

    /** 交易服务，处理交易相关的业务逻辑 */
    private final TransactionService transactionService;

    /**
     * 构造函数，注入交易服务依赖
     *
     * @param transactionService 交易服务实例
     */
    @Autowired
    public TransactionController(TransactionService transactionService) {
        this.transactionService = transactionService;
    }

    /**
     * 创建新交易
     * 根据请求参数创建一笔新的财务交易
     * 系统会自动验证借贷平衡，确保符合复式记账原理
     * 新创建的交易状态为PENDING，需要经过审批流程
     *
     * @param request 创建交易请求对象，包含交易描述、日期、分录列表等信息
     * @return 创建成功的交易对象，HTTP状态码201
     */
    @PostMapping
    public ResponseEntity<Transaction> createTransaction(@Valid @RequestBody CreateTransactionRequest request) {

        // Convert DTOs to entities
        List<TransactionEntry> entries = request.getEntries().stream()
                .map(this::convertToTransactionEntry)
                .toList();

        Transaction transaction = transactionService.createTransaction(
            request.getDescription(),
            request.getTransactionDate(),
            entries,
            request.getCurrencyId(),
            1L // TODO: Get from security context
        );

        return new ResponseEntity<>(transaction, HttpStatus.CREATED);
    }

    /**
     * 根据ID获取交易信息
     * 通过交易的唯一标识符查询交易详细信息
     * 包括交易基本信息和所有相关的交易分录
     *
     * @param id 交易ID，必须是有效的交易标识符
     * @return 交易对象，包含完整的交易信息和分录列表
     */
    @GetMapping("/{id}")
    public ResponseEntity<Transaction> getTransaction(@PathVariable Long id) {
        Transaction transaction = transactionService.getTransactionById(id);
        return ResponseEntity.ok(transaction);
    }

    /**
     * 根据交易编号获取交易信息
     * 通过交易编号（业务编号）查询交易详细信息
     * 交易编号是用户可见的业务标识，通常按时间顺序生成
     *
     * @param transactionNumber 交易编号，如"TXN202401001"等格式
     * @return 交易对象，包含完整的交易信息和分录列表
     */
    @GetMapping("/number/{transactionNumber}")
    public ResponseEntity<Transaction> getTransactionByNumber(@PathVariable String transactionNumber) {
        Transaction transaction = transactionService.getTransactionByNumber(transactionNumber);
        return ResponseEntity.ok(transaction);
    }

    /**
     * 获取交易列表
     * 支持多种查询方式：按状态筛选、日期范围查询、用户筛选或关键字搜索
     * 提供灵活的交易查询功能，满足不同业务场景需求
     * 如果不提供任何参数，默认返回待审批的交易
     *
     * @param status 交易状态筛选条件，可选参数（待审批、已审批、已过账、已冲销）
     * @param startDate 开始日期，可选参数，用于日期范围查询
     * @param endDate 结束日期，可选参数，用于日期范围查询
     * @param userId 用户ID，可选参数，查询特定用户创建的交易
     * @param search 搜索关键字，可选参数，支持按交易描述或编号模糊搜索
     * @return 符合条件的交易列表
     */
    @GetMapping
    public ResponseEntity<List<Transaction>> getTransactions(
            @RequestParam(required = false) TransactionStatus status,
            @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate startDate,
            @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate endDate,
            @RequestParam(required = false) Long userId,
            @RequestParam(required = false) String search) {

        List<Transaction> transactions;

        if (search != null && !search.trim().isEmpty()) {
            transactions = transactionService.searchTransactions(search.trim());
        } else if (startDate != null && endDate != null) {
            transactions = transactionService.getTransactionsByDateRange(startDate, endDate);
        } else if (status != null) {
            transactions = transactionService.getTransactionsByStatus(status);
        } else if (userId != null) {
            transactions = transactionService.getTransactionsByUser(userId);
        } else {
            // Default: get recent transactions
            transactions = transactionService.getTransactionsByStatus(TransactionStatus.PENDING);
        }

        return ResponseEntity.ok(transactions);
    }

    /**
     * 审批交易
     * 将待审批状态的交易标记为已审批
     * 只有具有审批权限的用户才能执行此操作
     * 审批后的交易可以进行过账操作
     *
     * @param id 要审批的交易ID
     * @return 审批后的交易对象
     */
    @PostMapping("/{id}/approve")
    public ResponseEntity<Transaction> approveTransaction(@PathVariable Long id) {
        Transaction transaction = transactionService.approveTransaction(id, 1L); // TODO: Get from security context
        return ResponseEntity.ok(transaction);
    }

    /**
     * 过账交易
     * 将已审批的交易过账到总账，更新相关账户余额
     * 过账是财务处理的最终步骤，一旦过账完成，交易将影响账户余额
     * 过账后的交易不能直接修改，只能通过冲销方式处理
     *
     * @param id 要过账的交易ID
     * @return 过账后的交易对象
     */
    @PostMapping("/{id}/post")
    public ResponseEntity<Transaction> postTransaction(@PathVariable Long id) {
        Transaction transaction = transactionService.postTransaction(id);
        return ResponseEntity.ok(transaction);
    }

    /**
     * 冲销交易
     * 创建一笔与原交易相反的冲销交易，用于纠正已过账的错误交易
     * 冲销是财务系统中处理错误交易的标准方法，保持审计追踪的完整性
     * 冲销交易会自动生成相反的分录，抵消原交易对账户余额的影响
     *
     * @param id 要冲销的原交易ID
     * @param reason 冲销原因，必须提供详细的说明
     * @return 新创建的冲销交易对象
     */
    @PostMapping("/{id}/reverse")
    public ResponseEntity<Transaction> reverseTransaction(@PathVariable Long id,
                                                        @RequestParam String reason) {
        Transaction reversalTransaction = transactionService.reverseTransaction(
            id, reason, 1L); // TODO: Get from security context
        return ResponseEntity.ok(reversalTransaction);
    }

    /**
     * 按状态获取交易列表
     * 查询指定状态的所有交易
     * 常用于工作流管理，如查看待审批、已过账的交易
     *
     * @param status 交易状态（PENDING、APPROVED、POSTED、REVERSED）
     * @return 指定状态的交易列表
     */
    @GetMapping("/status/{status}")
    public ResponseEntity<List<Transaction>> getTransactionsByStatus(@PathVariable TransactionStatus status) {
        List<Transaction> transactions = transactionService.getTransactionsByStatus(status);
        return ResponseEntity.ok(transactions);
    }

    /**
     * 按日期范围获取交易列表
     * 查询指定日期范围内的所有交易
     * 常用于财务报表生成和期间分析
     *
     * @param startDate 开始日期（包含），ISO日期格式
     * @param endDate 结束日期（包含），ISO日期格式
     * @return 指定日期范围内的交易列表
     */
    @GetMapping("/date-range")
    public ResponseEntity<List<Transaction>> getTransactionsByDateRange(
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate startDate,
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate endDate) {

        List<Transaction> transactions = transactionService.getTransactionsByDateRange(startDate, endDate);
        return ResponseEntity.ok(transactions);
    }

    /**
     * 按用户获取交易列表
     * 查询指定用户创建的所有交易
     * 用于用户工作量统计和个人交易历史查询
     *
     * @param userId 用户ID，要查询交易的用户标识符
     * @return 指定用户创建的交易列表
     */
    @GetMapping("/user/{userId}")
    public ResponseEntity<List<Transaction>> getTransactionsByUser(@PathVariable Long userId) {
        List<Transaction> transactions = transactionService.getTransactionsByUser(userId);
        return ResponseEntity.ok(transactions);
    }

    /**
     * 将交易分录请求DTO转换为实体对象
     * 私有辅助方法，用于在创建交易时转换数据格式
     *
     * @param request 交易分录请求DTO
     * @return 交易分录实体对象
     */
    private TransactionEntry convertToTransactionEntry(TransactionEntryRequest request) {
        return new TransactionEntry(
            null, // transactionId will be set by service
            request.getAccountId(),
            request.getEntryType(),
            request.getAmount(),
            request.getDescription()
        );
    }
}
