package org.example.ledgerbased.controller;

import org.example.ledgerbased.dto.CreateTransactionRequest;
import org.example.ledgerbased.dto.TransactionEntryRequest;
import org.example.ledgerbased.model.Transaction;
import org.example.ledgerbased.model.TransactionEntry;
import org.example.ledgerbased.model.TransactionStatus;
import org.example.ledgerbased.service.TransactionService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;
import java.time.LocalDate;
import java.util.List;

@RestController
@RequestMapping("/api/transactions")
public class TransactionController {

    private final TransactionService transactionService;

    @Autowired
    public TransactionController(TransactionService transactionService) {
        this.transactionService = transactionService;
    }

    @PostMapping
    public ResponseEntity<Transaction> createTransaction(@Valid @RequestBody CreateTransactionRequest request) {
        
        // Convert DTOs to entities
        List<TransactionEntry> entries = request.getEntries().stream()
                .map(this::convertToTransactionEntry)
                .toList();
        
        Transaction transaction = transactionService.createTransaction(
            request.getDescription(),
            request.getTransactionDate(),
            entries,
            request.getCurrencyId(),
            1L // TODO: Get from security context
        );
        
        return new ResponseEntity<>(transaction, HttpStatus.CREATED);
    }

    @GetMapping("/{id}")
    public ResponseEntity<Transaction> getTransaction(@PathVariable Long id) {
        Transaction transaction = transactionService.getTransactionById(id);
        return ResponseEntity.ok(transaction);
    }

    @GetMapping("/number/{transactionNumber}")
    public ResponseEntity<Transaction> getTransactionByNumber(@PathVariable String transactionNumber) {
        Transaction transaction = transactionService.getTransactionByNumber(transactionNumber);
        return ResponseEntity.ok(transaction);
    }

    @GetMapping
    public ResponseEntity<List<Transaction>> getTransactions(
            @RequestParam(required = false) TransactionStatus status,
            @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate startDate,
            @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate endDate,
            @RequestParam(required = false) Long userId,
            @RequestParam(required = false) String search) {
        
        List<Transaction> transactions;
        
        if (search != null && !search.trim().isEmpty()) {
            transactions = transactionService.searchTransactions(search.trim());
        } else if (startDate != null && endDate != null) {
            transactions = transactionService.getTransactionsByDateRange(startDate, endDate);
        } else if (status != null) {
            transactions = transactionService.getTransactionsByStatus(status);
        } else if (userId != null) {
            transactions = transactionService.getTransactionsByUser(userId);
        } else {
            // Default: get recent transactions
            transactions = transactionService.getTransactionsByStatus(TransactionStatus.PENDING);
        }
        
        return ResponseEntity.ok(transactions);
    }

    @PostMapping("/{id}/approve")
    public ResponseEntity<Transaction> approveTransaction(@PathVariable Long id) {
        Transaction transaction = transactionService.approveTransaction(id, 1L); // TODO: Get from security context
        return ResponseEntity.ok(transaction);
    }

    @PostMapping("/{id}/post")
    public ResponseEntity<Transaction> postTransaction(@PathVariable Long id) {
        Transaction transaction = transactionService.postTransaction(id);
        return ResponseEntity.ok(transaction);
    }

    @PostMapping("/{id}/reverse")
    public ResponseEntity<Transaction> reverseTransaction(@PathVariable Long id, 
                                                        @RequestParam String reason) {
        Transaction reversalTransaction = transactionService.reverseTransaction(
            id, reason, 1L); // TODO: Get from security context
        return ResponseEntity.ok(reversalTransaction);
    }

    @GetMapping("/status/{status}")
    public ResponseEntity<List<Transaction>> getTransactionsByStatus(@PathVariable TransactionStatus status) {
        List<Transaction> transactions = transactionService.getTransactionsByStatus(status);
        return ResponseEntity.ok(transactions);
    }

    @GetMapping("/date-range")
    public ResponseEntity<List<Transaction>> getTransactionsByDateRange(
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate startDate,
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate endDate) {
        
        List<Transaction> transactions = transactionService.getTransactionsByDateRange(startDate, endDate);
        return ResponseEntity.ok(transactions);
    }

    @GetMapping("/user/{userId}")
    public ResponseEntity<List<Transaction>> getTransactionsByUser(@PathVariable Long userId) {
        List<Transaction> transactions = transactionService.getTransactionsByUser(userId);
        return ResponseEntity.ok(transactions);
    }

    private TransactionEntry convertToTransactionEntry(TransactionEntryRequest request) {
        return new TransactionEntry(
            null, // transactionId will be set by service
            request.getAccountId(),
            request.getEntryType(),
            request.getAmount(),
            request.getDescription()
        );
    }
}
