package org.example.ledgerbased.controller;

import org.example.ledgerbased.dto.CreateAccountRequest;
import org.example.ledgerbased.model.Account;
import org.example.ledgerbased.model.AccountType;
import org.example.ledgerbased.service.AccountService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;
import java.math.BigDecimal;
import java.util.List;

/**
 * 账户管理控制器
 * 提供账户相关的RESTful API接口，包括账户的创建、查询、更新和删除操作
 * 支持层级账户结构和多种查询方式
 */
@RestController
@RequestMapping("/api/accounts")
public class AccountController {

    /** 账户服务，处理账户相关的业务逻辑 */
    private final AccountService accountService;

    /**
     * 构造函数，注入账户服务依赖
     *
     * @param accountService 账户服务实例
     */
    @Autowired
    public AccountController(AccountService accountService) {
        this.accountService = accountService;
    }

    /**
     * 创建新账户
     * 根据请求参数创建根账户或子账户
     * 如果指定了父账户ID，则创建子账户；否则创建根账户
     *
     * @param request 创建账户请求对象，包含账户基本信息
     * @return 创建成功的账户对象，HTTP状态码201
     */
    @PostMapping
    public ResponseEntity<Account> createAccount(@Valid @RequestBody CreateAccountRequest request) {
        Account account;

        if (request.getParentAccountId() != null) {
            // 创建子账户
            account = accountService.createSubAccount(
                request.getAccountNumber(),
                request.getAccountName(),
                request.getParentAccountId(),
                request.getCurrencyId(),
                request.getOpeningBalance(),
                1L // TODO: 从安全上下文获取当前用户ID
            );
        } else {
            // 创建根账户
            account = accountService.createAccount(
                request.getAccountNumber(),
                request.getAccountName(),
                request.getAccountType(),
                request.getCurrencyId(),
                request.getOpeningBalance(),
                1L // TODO: 从安全上下文获取当前用户ID
            );
        }

        return new ResponseEntity<>(account, HttpStatus.CREATED);
    }

    @GetMapping("/{id}")
    public ResponseEntity<Account> getAccount(@PathVariable Long id) {
        Account account = accountService.getAccountById(id);
        return ResponseEntity.ok(account);
    }

    @GetMapping("/number/{accountNumber}")
    public ResponseEntity<Account> getAccountByNumber(@PathVariable String accountNumber) {
        Account account = accountService.getAccountByNumber(accountNumber);
        return ResponseEntity.ok(account);
    }

    @GetMapping
    public ResponseEntity<List<Account>> getAllAccounts(
            @RequestParam(required = false) AccountType type,
            @RequestParam(required = false) String search) {
        
        List<Account> accounts;
        
        if (search != null && !search.trim().isEmpty()) {
            accounts = accountService.searchAccounts(search.trim());
        } else if (type != null) {
            accounts = accountService.getAccountsByType(type);
        } else {
            accounts = accountService.getAllActiveAccounts();
        }
        
        return ResponseEntity.ok(accounts);
    }

    @GetMapping("/root")
    public ResponseEntity<List<Account>> getRootAccounts() {
        List<Account> accounts = accountService.getRootAccounts();
        return ResponseEntity.ok(accounts);
    }

    @GetMapping("/{parentId}/sub-accounts")
    public ResponseEntity<List<Account>> getSubAccounts(@PathVariable Long parentId) {
        List<Account> accounts = accountService.getSubAccounts(parentId);
        return ResponseEntity.ok(accounts);
    }

    @PutMapping("/{id}")
    public ResponseEntity<Account> updateAccount(@PathVariable Long id, 
                                               @RequestParam String accountName) {
        Account account = accountService.updateAccount(id, accountName);
        return ResponseEntity.ok(account);
    }

    @DeleteMapping("/{id}")
    public ResponseEntity<Void> deactivateAccount(@PathVariable Long id) {
        accountService.deactivateAccount(id);
        return ResponseEntity.noContent().build();
    }

    @GetMapping("/balance-sheet")
    public ResponseEntity<List<Account>> getBalanceSheetAccounts() {
        List<Account> accounts = accountService.getAccountsForBalanceSheet();
        return ResponseEntity.ok(accounts);
    }

    @GetMapping("/income-statement")
    public ResponseEntity<List<Account>> getIncomeStatementAccounts() {
        List<Account> accounts = accountService.getAccountsForIncomeStatement();
        return ResponseEntity.ok(accounts);
    }

    @GetMapping("/totals/{accountType}")
    public ResponseEntity<BigDecimal> getTotalByAccountType(@PathVariable AccountType accountType) {
        BigDecimal total = accountService.getTotalBalanceByAccountType(accountType);
        return ResponseEntity.ok(total);
    }

    @GetMapping("/validate-equation")
    public ResponseEntity<Boolean> validateAccountingEquation() {
        boolean isValid = accountService.validateAccountingEquation();
        return ResponseEntity.ok(isValid);
    }
}
