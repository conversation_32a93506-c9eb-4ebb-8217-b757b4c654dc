package org.example.ledgerbased.controller;

import org.example.ledgerbased.dto.CreateAccountRequest;
import org.example.ledgerbased.model.Account;
import org.example.ledgerbased.model.AccountType;
import org.example.ledgerbased.service.AccountService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;
import java.math.BigDecimal;
import java.util.List;

/**
 * 账户管理控制器
 * 提供账户相关的RESTful API接口，包括账户的创建、查询、更新和删除操作
 * 支持层级账户结构和多种查询方式
 */
@RestController
@RequestMapping("/api/accounts")
public class AccountController {

    /** 账户服务，处理账户相关的业务逻辑 */
    private final AccountService accountService;

    /**
     * 构造函数，注入账户服务依赖
     *
     * @param accountService 账户服务实例
     */
    @Autowired
    public AccountController(AccountService accountService) {
        this.accountService = accountService;
    }

    /**
     * 创建新账户
     * 根据请求参数创建根账户或子账户
     * 如果指定了父账户ID，则创建子账户；否则创建根账户
     *
     * @param request 创建账户请求对象，包含账户基本信息
     * @return 创建成功的账户对象，HTTP状态码201
     */
    @PostMapping
    public ResponseEntity<Account> createAccount(@Valid @RequestBody CreateAccountRequest request) {
        Account account;

        if (request.getParentAccountId() != null) {
            // 创建子账户
            account = accountService.createSubAccount(
                request.getAccountNumber(),
                request.getAccountName(),
                request.getParentAccountId(),
                request.getCurrencyId(),
                request.getOpeningBalance(),
                1L // TODO: 从安全上下文获取当前用户ID
            );
        } else {
            // 创建根账户
            account = accountService.createAccount(
                request.getAccountNumber(),
                request.getAccountName(),
                request.getAccountType(),
                request.getCurrencyId(),
                request.getOpeningBalance(),
                1L // TODO: 从安全上下文获取当前用户ID
            );
        }

        return new ResponseEntity<>(account, HttpStatus.CREATED);
    }

    /**
     * 根据ID获取账户信息
     * 通过账户的唯一标识符查询账户详细信息
     *
     * @param id 账户ID，必须是有效的账户标识符
     * @return 账户对象，包含完整的账户信息
     */
    @GetMapping("/{id}")
    public ResponseEntity<Account> getAccount(@PathVariable Long id) {
        Account account = accountService.getAccountById(id);
        return ResponseEntity.ok(account);
    }

    /**
     * 根据账户编号获取账户信息
     * 通过账户编号（业务编号）查询账户详细信息
     * 账户编号是用户可见的业务标识，通常遵循会计科目编码规则
     *
     * @param accountNumber 账户编号，如"1001"、"2001"等会计科目编码
     * @return 账户对象，包含完整的账户信息
     */
    @GetMapping("/number/{accountNumber}")
    public ResponseEntity<Account> getAccountByNumber(@PathVariable String accountNumber) {
        Account account = accountService.getAccountByNumber(accountNumber);
        return ResponseEntity.ok(account);
    }

    /**
     * 获取账户列表
     * 支持多种查询方式：按类型筛选、关键字搜索或获取所有激活账户
     * 提供灵活的账户查询功能，满足不同业务场景需求
     *
     * @param type 账户类型筛选条件，可选参数（资产、负债、权益、收入、费用）
     * @param search 搜索关键字，可选参数，支持按账户名称或编号模糊搜索
     * @return 符合条件的账户列表
     */
    @GetMapping
    public ResponseEntity<List<Account>> getAllAccounts(
            @RequestParam(required = false) AccountType type,
            @RequestParam(required = false) String search) {

        List<Account> accounts;

        if (search != null && !search.trim().isEmpty()) {
            accounts = accountService.searchAccounts(search.trim());
        } else if (type != null) {
            accounts = accountService.getAccountsByType(type);
        } else {
            accounts = accountService.getAllActiveAccounts();
        }

        return ResponseEntity.ok(accounts);
    }

    /**
     * 获取所有根账户
     * 返回会计科目表中的顶级账户（没有父账户的账户）
     * 根账户通常对应主要的会计科目分类，如资产、负债、权益等
     *
     * @return 根账户列表，用于构建层级账户结构
     */
    @GetMapping("/root")
    public ResponseEntity<List<Account>> getRootAccounts() {
        List<Account> accounts = accountService.getRootAccounts();
        return ResponseEntity.ok(accounts);
    }

    /**
     * 获取指定账户的子账户
     * 返回某个父账户下的所有直接子账户
     * 支持层级账户结构的导航和展示
     *
     * @param parentId 父账户ID，用于查询其下级账户
     * @return 子账户列表，按账户编号排序
     */
    @GetMapping("/{parentId}/sub-accounts")
    public ResponseEntity<List<Account>> getSubAccounts(@PathVariable Long parentId) {
        List<Account> accounts = accountService.getSubAccounts(parentId);
        return ResponseEntity.ok(accounts);
    }

    /**
     * 更新账户信息
     * 修改账户的基本信息，如账户名称
     * 注意：账户编号、类型等关键信息不允许修改，以保证财务数据的完整性
     *
     * @param id 账户ID，要更新的账户标识符
     * @param accountName 新的账户名称，不能为空
     * @return 更新后的账户对象
     */
    @PutMapping("/{id}")
    public ResponseEntity<Account> updateAccount(@PathVariable Long id,
                                               @RequestParam String accountName) {
        Account account = accountService.updateAccount(id, accountName);
        return ResponseEntity.ok(account);
    }

    /**
     * 停用账户
     * 将账户标记为非激活状态，而不是物理删除
     * 停用的账户不能再进行新的交易，但历史交易记录仍然保留
     * 这符合财务系统的审计要求和数据保留原则
     *
     * @param id 要停用的账户ID
     * @return 无内容响应，HTTP状态码204
     */
    @DeleteMapping("/{id}")
    public ResponseEntity<Void> deactivateAccount(@PathVariable Long id) {
        accountService.deactivateAccount(id);
        return ResponseEntity.noContent().build();
    }

    /**
     * 获取资产负债表相关账户
     * 返回用于生成资产负债表的账户列表
     * 包括资产类、负债类和权益类账户
     * 这些账户反映企业在特定时点的财务状况
     *
     * @return 资产负债表账户列表，按账户类型和编号排序
     */
    @GetMapping("/balance-sheet")
    public ResponseEntity<List<Account>> getBalanceSheetAccounts() {
        List<Account> accounts = accountService.getAccountsForBalanceSheet();
        return ResponseEntity.ok(accounts);
    }

    /**
     * 获取利润表相关账户
     * 返回用于生成利润表（损益表）的账户列表
     * 包括收入类和费用类账户
     * 这些账户反映企业在特定期间的经营成果
     *
     * @return 利润表账户列表，按账户类型和编号排序
     */
    @GetMapping("/income-statement")
    public ResponseEntity<List<Account>> getIncomeStatementAccounts() {
        List<Account> accounts = accountService.getAccountsForIncomeStatement();
        return ResponseEntity.ok(accounts);
    }

    /**
     * 按账户类型获取总余额
     * 计算指定账户类型下所有账户的余额总和
     * 用于财务报表汇总和会计等式验证
     *
     * @param accountType 账户类型（资产、负债、权益、收入、费用）
     * @return 该类型账户的总余额
     */
    @GetMapping("/totals/{accountType}")
    public ResponseEntity<BigDecimal> getTotalByAccountType(@PathVariable AccountType accountType) {
        BigDecimal total = accountService.getTotalBalanceByAccountType(accountType);
        return ResponseEntity.ok(total);
    }

    /**
     * 验证会计等式
     * 检查系统中的账户余额是否符合基本会计等式：资产 = 负债 + 权益
     * 这是复式记账系统的基本原则，确保账务数据的准确性和完整性
     * 如果等式不平衡，说明系统存在数据错误，需要进行调查和修正
     *
     * @return 会计等式验证结果，true表示平衡，false表示不平衡
     */
    @GetMapping("/validate-equation")
    public ResponseEntity<Boolean> validateAccountingEquation() {
        boolean isValid = accountService.validateAccountingEquation();
        return ResponseEntity.ok(isValid);
    }
}
