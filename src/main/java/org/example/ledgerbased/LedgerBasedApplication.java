package org.example.ledgerbased;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;

/**
 * 账本交易管理系统主应用类
 *
 * 这是一个基于Spring Boot的财务交易管理系统，实现了复式记账原理
 * 主要功能包括：
 * - 账户管理：支持层级结构的会计科目表
 * - 交易处理：实现完整的交易生命周期管理
 * - 多币种支持：支持多种货币的交易处理
 * - 权限控制：基于角色的访问控制
 * - 审计追踪：完整的操作审计日志
 *
 * 系统遵循财务行业最佳实践，确保数据完整性和合规性
 *
 * <AUTHOR> Development Team
 * @version 1.0
 * @since 2024-01-01
 */
@SpringBootApplication
public class LedgerBasedApplication {

    /**
     * 应用程序入口点
     * 启动Spring Boot应用程序，初始化所有必要的组件和服务
     *
     * @param args 命令行参数，可用于传递配置参数
     */
    public static void main(String[] args) {
        SpringApplication.run(LedgerBasedApplication.class, args);
    }

}
