package org.example.ledgerbased.model;

import org.springframework.data.annotation.Id;
import org.springframework.data.relational.core.mapping.Table;

import java.time.LocalDateTime;

/**
 * 用户实体类，表示系统中的用户信息
 * 支持基于角色的访问控制，用于财务系统的用户管理
 */
@Table("users")
public class User {

    /** 用户唯一标识符，主键 */
    @Id
    private Long id;

    /** 用户名，用于登录系统，必须唯一 */
    private String username;

    /** 密码哈希值，使用BCrypt加密存储 */
    private String passwordHash;

    /** 用户邮箱地址，必须唯一，用于通知和密码重置 */
    private String email;

    /** 用户名字 */
    private String firstName;

    /** 用户姓氏 */
    private String lastName;

    /** 用户角色，决定用户在系统中的权限级别 */
    private UserRole role;

    /** 用户是否激活状态，false表示用户被禁用 */
    private boolean isActive;

    /** 用户创建时间戳 */
    private LocalDateTime createdAt;

    /** 用户信息最后更新时间戳 */
    private LocalDateTime updatedAt;

    public User() {}

    public User(String username, String passwordHash, String email, 
                String firstName, String lastName, UserRole role) {
        this.username = username;
        this.passwordHash = passwordHash;
        this.email = email;
        this.firstName = firstName;
        this.lastName = lastName;
        this.role = role;
        this.isActive = true;
        this.createdAt = LocalDateTime.now();
        this.updatedAt = LocalDateTime.now();
    }

    // Getters and Setters
    public Long getId() { return id; }
    public void setId(Long id) { this.id = id; }

    public String getUsername() { return username; }
    public void setUsername(String username) { this.username = username; }

    public String getPasswordHash() { return passwordHash; }
    public void setPasswordHash(String passwordHash) { this.passwordHash = passwordHash; }

    public String getEmail() { return email; }
    public void setEmail(String email) { this.email = email; }

    public String getFirstName() { return firstName; }
    public void setFirstName(String firstName) { this.firstName = firstName; }

    public String getLastName() { return lastName; }
    public void setLastName(String lastName) { this.lastName = lastName; }

    public UserRole getRole() { return role; }
    public void setRole(UserRole role) { this.role = role; }

    public boolean isActive() { return isActive; }
    public void setActive(boolean active) { isActive = active; }

    public LocalDateTime getCreatedAt() { return createdAt; }
    public void setCreatedAt(LocalDateTime createdAt) { this.createdAt = createdAt; }

    public LocalDateTime getUpdatedAt() { return updatedAt; }
    public void setUpdatedAt(LocalDateTime updatedAt) { this.updatedAt = updatedAt; }

    public String getFullName() {
        return firstName + " " + lastName;
    }

    @Override
    public String toString() {
        return "User{" +
                "id=" + id +
                ", username='" + username + '\'' +
                ", email='" + email + '\'' +
                ", firstName='" + firstName + '\'' +
                ", lastName='" + lastName + '\'' +
                ", role=" + role +
                ", isActive=" + isActive +
                '}';
    }
}
