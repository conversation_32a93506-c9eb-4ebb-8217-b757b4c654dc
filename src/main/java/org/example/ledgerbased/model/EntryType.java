package org.example.ledgerbased.model;

/**
 * 分录类型枚举
 *
 * 定义复式记账中的借贷方向，是复式记账的核心概念
 * 每个交易分录必须明确指定是借方还是贷方
 *
 * 复式记账原理：
 * - 每笔交易至少涉及两个账户
 * - 借方总额必须等于贷方总额
 * - 借贷双方记录的是资金的来源和去向
 */
public enum EntryType {
    /**
     * 借方分录
     * 借方的作用：
     * - 增加资产账户余额
     * - 增加费用账户余额
     * - 减少负债账户余额
     * - 减少权益账户余额
     * - 减少收入账户余额
     */
    DEBIT("Debit entry - increases assets and expenses, decreases liabilities, equity, and revenue"),

    /**
     * 贷方分录
     * 贷方的作用：
     * - 增加负债账户余额
     * - 增加权益账户余额
     * - 增加收入账户余额
     * - 减少资产账户余额
     * - 减少费用账户余额
     */
    CREDIT("Credit entry - increases liabilities, equity, and revenue, decreases assets and expenses");

    /** 分录类型描述 */
    private final String description;

    /**
     * 构造函数
     *
     * @param description 分录类型描述
     */
    EntryType(String description) {
        this.description = description;
    }

    /**
     * 获取分录类型描述
     *
     * @return 分录类型的详细描述
     */
    public String getDescription() {
        return description;
    }

    /**
     * 获取相反的分录类型
     *
     * 在某些业务场景中需要获取相反的分录类型，如冲销交易
     *
     * @return 相反的分录类型
     */
    public EntryType opposite() {
        return this == DEBIT ? CREDIT : DEBIT;
    }

    /**
     * 获取分录类型的会计符号
     *
     * 在会计记录中使用标准的缩写符号：
     * - Dr表示借方（Debit）
     * - Cr表示贷方（Credit）
     *
     * @return 会计符号字符串
     */
    public String getSymbol() {
        return this == DEBIT ? "Dr" : "Cr";
    }
}
