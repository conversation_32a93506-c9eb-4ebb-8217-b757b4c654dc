package org.example.ledgerbased.model;

/**
 * 交易状态枚举
 *
 * 定义交易在系统中的生命周期状态，实现严格的状态控制
 * 确保交易按照正确的流程进行，维护财务数据的完整性
 *
 * 交易状态流转：PENDING → APPROVED → POSTED
 * 特殊状态：REVERSED（已冲销）
 */
public enum TransactionStatus {
    /**
     * 待审批状态
     * 交易已创建但尚未审批，可以修改或删除
     * 此状态下交易不影响账户余额
     */
    PENDING("Transaction created but not yet approved"),

    /**
     * 已审批状态
     * 交易已通过审批但尚未过账到总账
     * 此状态下交易不能修改，但尚未影响账户余额
     */
    APPROVED("Transaction approved but not yet posted to ledger"),

    /**
     * 已过账状态
     * 交易已过账到总账，影响相关账户余额
     * 此状态下交易不能修改，只能通过冲销进行调整
     */
    POSTED("Transaction posted to ledger and affecting account balances"),

    /**
     * 已冲销状态
     * 交易已被冲销，通过创建相反的交易来抵消原交易的影响
     * 冲销是财务系统中修正错误交易的标准做法
     */
    REVERSED("Transaction has been reversed");

    /** 状态描述 */
    private final String description;

    /**
     * 构造函数
     *
     * @param description 状态描述
     */
    TransactionStatus(String description) {
        this.description = description;
    }

    /**
     * 获取状态描述
     *
     * @return 状态的详细描述
     */
    public String getDescription() {
        return description;
    }

    /**
     * 判断交易是否可以修改
     *
     * 只有待审批状态的交易可以修改
     * 已审批或已过账的交易不能修改，确保审计追踪的完整性
     *
     * @return true表示可以修改，false表示不可修改
     */
    public boolean canBeModified() {
        return this == PENDING;
    }

    /**
     * 判断交易是否可以审批
     *
     * 只有待审批状态的交易可以进行审批操作
     *
     * @return true表示可以审批，false表示不可审批
     */
    public boolean canBeApproved() {
        return this == PENDING;
    }

    /**
     * 判断交易是否可以过账
     *
     * 只有已审批状态的交易可以过账到总账
     * 过账后交易将影响相关账户的余额
     *
     * @return true表示可以过账，false表示不可过账
     */
    public boolean canBePosted() {
        return this == APPROVED;
    }

    /**
     * 判断交易是否可以冲销
     *
     * 只有已过账状态的交易可以进行冲销
     * 冲销通过创建相反的交易来抵消原交易的影响
     *
     * @return true表示可以冲销，false表示不可冲销
     */
    public boolean canBeReversed() {
        return this == POSTED;
    }

    /**
     * 判断交易是否影响账户余额
     *
     * 只有已过账状态的交易才会影响账户余额
     * 其他状态的交易都不会改变账户余额
     *
     * @return true表示影响余额，false表示不影响余额
     */
    public boolean affectsBalance() {
        return this == POSTED;
    }
}
