package org.example.ledgerbased.model;

/**
 * 用户角色枚举，定义系统中不同用户的权限级别
 * 实现基于角色的访问控制(RBAC)，确保财务系统的安全性
 */
public enum UserRole {
    /** 会计师角色 - 可以创建和修改交易 */
    ACCOUNTANT("Can create and modify transactions"),

    /** 审批者角色 - 可以审批交易 */
    APPROVER("Can approve transactions"),

    /** 审计员角色 - 可以查看所有交易和报告 */
    AUDITOR("Can view all transactions and reports"),

    /** 管理员角色 - 拥有系统完全访问权限 */
    ADMIN("Full system access");

    /** 角色描述信息 */
    private final String description;

    /**
     * 构造函数
     *
     * @param description 角色描述
     */
    UserRole(String description) {
        this.description = description;
    }

    /**
     * 获取角色描述
     *
     * @return 角色的详细描述
     */
    public String getDescription() {
        return description;
    }

    /**
     * 检查当前角色是否可以创建交易
     *
     * @return true如果可以创建交易，否则false
     */
    public boolean canCreateTransactions() {
        return this == ACCOUNTANT || this == ADMIN;
    }

    /**
     * 检查当前角色是否可以审批交易
     *
     * @return true如果可以审批交易，否则false
     */
    public boolean canApproveTransactions() {
        return this == APPROVER || this == ADMIN;
    }

    /**
     * 检查当前角色是否可以查看所有交易
     *
     * @return true如果可以查看所有交易，否则false
     */
    public boolean canViewAllTransactions() {
        return this == AUDITOR || this == ADMIN;
    }

    /**
     * 检查当前角色是否可以管理用户
     *
     * @return true如果可以管理用户，否则false
     */
    public boolean canManageUsers() {
        return this == ADMIN;
    }

    /**
     * 检查当前角色是否可以管理账户
     *
     * @return true如果可以管理账户，否则false
     */
    public boolean canManageAccounts() {
        return this == ADMIN;
    }
}
