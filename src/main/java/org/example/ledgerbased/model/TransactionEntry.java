package org.example.ledgerbased.model;

import org.springframework.data.annotation.Id;
import org.springframework.data.relational.core.mapping.Table;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 交易分录实体类
 *
 * 表示复式记账中的单个分录，是交易的基本组成单元
 * 每个交易由多个分录组成，每个分录记录一个账户的借方或贷方变动
 *
 * 复式记账要求：
 * - 每个交易至少包含两个分录
 * - 所有借方分录金额之和必须等于所有贷方分录金额之和
 * - 每个分录必须明确指定账户、金额和借贷方向
 */
@Table("transaction_entries")
public class TransactionEntry {

    /** 分录唯一标识符，主键 */
    @Id
    private Long id;

    /** 所属交易ID，关联到交易表 */
    private Long transactionId;

    /** 账户ID，关联到账户表，指定此分录影响的账户 */
    private Long accountId;

    /** 分录类型，借方或贷方 */
    private EntryType entryType;

    /** 分录金额，必须为正数 */
    private BigDecimal amount;

    /** 分录描述，说明此分录的业务含义 */
    private String description;

    /** 分录创建时间戳 */
    private LocalDateTime createdAt;

    public TransactionEntry() {}

    public TransactionEntry(Long transactionId, Long accountId, EntryType entryType,
                           BigDecimal amount, String description) {
        this.transactionId = transactionId;
        this.accountId = accountId;
        this.entryType = entryType;
        this.amount = amount;
        this.description = description;
        this.createdAt = LocalDateTime.now();
    }

    // Getters and Setters
    public Long getId() { return id; }
    public void setId(Long id) { this.id = id; }

    public Long getTransactionId() { return transactionId; }
    public void setTransactionId(Long transactionId) { this.transactionId = transactionId; }

    public Long getAccountId() { return accountId; }
    public void setAccountId(Long accountId) { this.accountId = accountId; }

    public EntryType getEntryType() { return entryType; }
    public void setEntryType(EntryType entryType) { this.entryType = entryType; }

    public BigDecimal getAmount() { return amount; }
    public void setAmount(BigDecimal amount) { this.amount = amount; }

    public String getDescription() { return description; }
    public void setDescription(String description) { this.description = description; }

    public LocalDateTime getCreatedAt() { return createdAt; }
    public void setCreatedAt(LocalDateTime createdAt) { this.createdAt = createdAt; }

    // Business methods
    public boolean isDebit() {
        return entryType == EntryType.DEBIT;
    }

    public boolean isCredit() {
        return entryType == EntryType.CREDIT;
    }

    public String getFormattedAmount() {
        return entryType.getSymbol() + " " + amount.toString();
    }

    @Override
    public String toString() {
        return "TransactionEntry{" +
                "id=" + id +
                ", transactionId=" + transactionId +
                ", accountId=" + accountId +
                ", entryType=" + entryType +
                ", amount=" + amount +
                ", description='" + description + '\'' +
                '}';
    }
}
