package org.example.ledgerbased.model;

import org.springframework.data.annotation.Id;
import org.springframework.data.relational.core.mapping.Table;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 账户实体类，表示财务系统中的会计科目
 * 支持层级结构和多币种，实现完整的会计科目表管理
 */
@Table("accounts")
public class Account {

    /** 账户唯一标识符，主键 */
    @Id
    private Long id;

    /** 账户编号，必须唯一，用于标识账户 */
    private String accountNumber;

    /** 账户名称，描述账户用途 */
    private String accountName;

    /** 账户类型，决定账户在财务报表中的分类 */
    private AccountType accountType;

    /** 父账户ID，用于构建账户层级结构，null表示根账户 */
    private Long parentAccountId;

    /** 货币ID，关联到货币表，支持多币种账户 */
    private Long currencyId;

    /** 期初余额，账户创建时的初始金额 */
    private BigDecimal openingBalance;

    /** 当前余额，账户的实时余额 */
    private BigDecimal currentBalance;

    /** 账户是否激活状态，false表示账户被禁用 */
    private boolean isActive;

    /** 创建者用户ID，记录账户创建人 */
    private Long createdBy;

    /** 账户创建时间戳 */
    private LocalDateTime createdAt;

    /** 账户信息最后更新时间戳 */
    private LocalDateTime updatedAt;

    public Account() {}

    public Account(String accountNumber, String accountName, AccountType accountType,
                   Long currencyId, BigDecimal openingBalance, Long createdBy) {
        this.accountNumber = accountNumber;
        this.accountName = accountName;
        this.accountType = accountType;
        this.currencyId = currencyId;
        this.openingBalance = openingBalance != null ? openingBalance : BigDecimal.ZERO;
        this.currentBalance = this.openingBalance;
        this.isActive = true;
        this.createdBy = createdBy;
        this.createdAt = LocalDateTime.now();
        this.updatedAt = LocalDateTime.now();
    }

    // Getters and Setters
    public Long getId() { return id; }
    public void setId(Long id) { this.id = id; }

    public String getAccountNumber() { return accountNumber; }
    public void setAccountNumber(String accountNumber) { this.accountNumber = accountNumber; }

    public String getAccountName() { return accountName; }
    public void setAccountName(String accountName) { this.accountName = accountName; }

    public AccountType getAccountType() { return accountType; }
    public void setAccountType(AccountType accountType) { this.accountType = accountType; }

    public Long getParentAccountId() { return parentAccountId; }
    public void setParentAccountId(Long parentAccountId) { this.parentAccountId = parentAccountId; }

    public Long getCurrencyId() { return currencyId; }
    public void setCurrencyId(Long currencyId) { this.currencyId = currencyId; }

    public BigDecimal getOpeningBalance() { return openingBalance; }
    public void setOpeningBalance(BigDecimal openingBalance) { this.openingBalance = openingBalance; }

    public BigDecimal getCurrentBalance() { return currentBalance; }
    public void setCurrentBalance(BigDecimal currentBalance) { this.currentBalance = currentBalance; }

    public boolean isActive() { return isActive; }
    public void setActive(boolean active) { isActive = active; }

    public Long getCreatedBy() { return createdBy; }
    public void setCreatedBy(Long createdBy) { this.createdBy = createdBy; }

    public LocalDateTime getCreatedAt() { return createdAt; }
    public void setCreatedAt(LocalDateTime createdAt) { this.createdAt = createdAt; }

    public LocalDateTime getUpdatedAt() { return updatedAt; }
    public void setUpdatedAt(LocalDateTime updatedAt) { this.updatedAt = updatedAt; }

    // Business methods
    public boolean isSubAccount() {
        return parentAccountId != null;
    }

    public String getFullAccountName() {
        return accountNumber + " - " + accountName;
    }

    public void updateBalance(BigDecimal amount, EntryType entryType) {
        if (accountType.increasesWithDebit() && entryType == EntryType.DEBIT) {
            currentBalance = currentBalance.add(amount);
        } else if (accountType.increasesWithDebit() && entryType == EntryType.CREDIT) {
            currentBalance = currentBalance.subtract(amount);
        } else if (!accountType.increasesWithDebit() && entryType == EntryType.CREDIT) {
            currentBalance = currentBalance.add(amount);
        } else {
            currentBalance = currentBalance.subtract(amount);
        }
        updatedAt = LocalDateTime.now();
    }

    @Override
    public String toString() {
        return "Account{" +
                "id=" + id +
                ", accountNumber='" + accountNumber + '\'' +
                ", accountName='" + accountName + '\'' +
                ", accountType=" + accountType +
                ", currentBalance=" + currentBalance +
                ", isActive=" + isActive +
                '}';
    }
}
