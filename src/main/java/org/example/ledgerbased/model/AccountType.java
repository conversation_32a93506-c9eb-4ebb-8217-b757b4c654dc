package org.example.ledgerbased.model;

/**
 * 账户类型枚举
 *
 * 定义会计学中的五大基本账户类型，遵循复式记账原理
 * 每种账户类型都有其特定的借贷规则和在财务报表中的位置
 *
 * 会计等式：资产 = 负债 + 所有者权益
 * 损益等式：收入 - 费用 = 净利润
 */
public enum AccountType {
    /** 资产账户 - 企业拥有的资源，如现金、应收账款、固定资产等 */
    ASSET("Assets - Resources owned by the company"),

    /** 负债账户 - 企业欠他人的债务，如应付账款、银行贷款等 */
    LIABILITY("Liabilities - Debts owed by the company"),

    /** 权益账户 - 所有者对企业的权益，如实收资本、留存收益等 */
    EQUITY("Equity - Owner's interest in the company"),

    /** 收入账户 - 企业经营活动产生的收入，如销售收入、服务收入等 */
    REVENUE("Revenue - Income earned by the company"),

    /** 费用账户 - 企业经营活动发生的成本，如销售费用、管理费用等 */
    EXPENSE("Expenses - Costs incurred by the company");

    /** 账户类型描述 */
    private final String description;

    /**
     * 构造函数
     *
     * @param description 账户类型描述
     */
    AccountType(String description) {
        this.description = description;
    }

    /**
     * 获取账户类型描述
     *
     * @return 账户类型的详细描述
     */
    public String getDescription() {
        return description;
    }

    /**
     * Determines the normal balance side for this account type
     * @return true if normal balance is debit, false if credit
     */
    public boolean isNormalBalanceDebit() {
        return this == ASSET || this == EXPENSE;
    }

    /**
     * Determines if this account type increases with debits
     * @return true if increases with debits, false if increases with credits
     */
    public boolean increasesWithDebit() {
        return isNormalBalanceDebit();
    }

    /**
     * Determines if this account type can have a negative balance
     * @return true if negative balance is allowed
     */
    public boolean allowsNegativeBalance() {
        // Generally, only liability and equity accounts can have negative balances
        // in certain circumstances, but for strict accounting, we'll be restrictive
        return false;
    }

    /**
     * Gets the financial statement category
     * @return the statement where this account type appears
     */
    public String getFinancialStatement() {
        switch (this) {
            case ASSET:
            case LIABILITY:
            case EQUITY:
                return "Balance Sheet";
            case REVENUE:
            case EXPENSE:
                return "Income Statement";
            default:
                return "Unknown";
        }
    }
}
