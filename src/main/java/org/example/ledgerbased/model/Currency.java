package org.example.ledgerbased.model;

import org.springframework.data.annotation.Id;
import org.springframework.data.relational.core.mapping.Table;

import java.time.LocalDateTime;

/**
 * 货币实体类
 *
 * 表示系统支持的货币类型，用于多币种财务管理
 * 支持不同货币的精度设置和状态管理
 * 为多币种交易和汇率转换提供基础数据
 */
@Table("currencies")
public class Currency {

    /** 货币唯一标识符，主键 */
    @Id
    private Long id;

    /** 货币代码，如USD、EUR、CNY等，遵循ISO 4217标准 */
    private String code;

    /** 货币名称，如美元、欧元、人民币等 */
    private String name;

    /** 货币符号，如$、€、¥等 */
    private String symbol;

    /** 小数位数，用于确定货币的精度，如美元为2位小数 */
    private Integer decimalPlaces;

    /** 货币是否激活状态，false表示货币被禁用 */
    private boolean isActive;

    /** 货币创建时间戳 */
    private LocalDateTime createdAt;

    public Currency() {}

    public Currency(String code, String name, String symbol, Integer decimalPlaces) {
        this.code = code;
        this.name = name;
        this.symbol = symbol;
        this.decimalPlaces = decimalPlaces;
        this.isActive = true;
        this.createdAt = LocalDateTime.now();
    }

    // Getters and Setters
    public Long getId() { return id; }
    public void setId(Long id) { this.id = id; }

    public String getCode() { return code; }
    public void setCode(String code) { this.code = code; }

    public String getName() { return name; }
    public void setName(String name) { this.name = name; }

    public String getSymbol() { return symbol; }
    public void setSymbol(String symbol) { this.symbol = symbol; }

    public Integer getDecimalPlaces() { return decimalPlaces; }
    public void setDecimalPlaces(Integer decimalPlaces) { this.decimalPlaces = decimalPlaces; }

    public boolean isActive() { return isActive; }
    public void setActive(boolean active) { isActive = active; }

    public LocalDateTime getCreatedAt() { return createdAt; }
    public void setCreatedAt(LocalDateTime createdAt) { this.createdAt = createdAt; }

    @Override
    public String toString() {
        return "Currency{" +
                "id=" + id +
                ", code='" + code + '\'' +
                ", name='" + name + '\'' +
                ", symbol='" + symbol + '\'' +
                ", decimalPlaces=" + decimalPlaces +
                ", isActive=" + isActive +
                '}';
    }
}
