package org.example.ledgerbased.repository;

import org.example.ledgerbased.model.User;
import org.example.ledgerbased.model.UserRole;
import org.springframework.data.jdbc.repository.query.Query;
import org.springframework.data.repository.CrudRepository;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

/**
 * 用户数据访问接口
 *
 * 提供用户相关的数据库操作方法，支持用户认证、授权和管理功能
 * 包括基本的CRUD操作和复杂的查询功能
 * 为系统的安全性和用户管理提供数据支持
 */
@Repository
public interface UserRepository extends CrudRepository<User, Long> {

    /**
     * 根据用户名查找用户
     *
     * 主要用于用户登录认证，用户名必须唯一
     *
     * @param username 用户名
     * @return 用户对象的Optional包装，如果不存在则为空
     */
    Optional<User> findByUsername(String username);

    /**
     * 根据邮箱地址查找用户
     *
     * 用于邮箱验证、密码重置等功能，邮箱地址必须唯一
     *
     * @param email 邮箱地址
     * @return 用户对象的Optional包装，如果不存在则为空
     */
    Optional<User> findByEmail(String email);

    /**
     * 根据用户角色查找所有用户
     *
     * 用于角色管理和权限分配
     *
     * @param role 用户角色
     * @return 指定角色的用户列表
     */
    List<User> findByRole(UserRole role);

    /**
     * 查找所有激活状态的用户
     *
     * 只返回激活的用户，过滤掉被禁用的用户账户
     *
     * @return 激活用户列表
     */
    List<User> findByIsActiveTrue();

    @Query("SELECT * FROM users WHERE role = :role AND is_active = true")
    List<User> findActiveUsersByRole(@Param("role") UserRole role);

    @Query("SELECT COUNT(*) FROM users WHERE role = :role AND is_active = true")
    long countActiveUsersByRole(@Param("role") UserRole role);

    @Query("SELECT * FROM users WHERE (first_name ILIKE :searchTerm OR last_name ILIKE :searchTerm OR username ILIKE :searchTerm) AND is_active = true")
    List<User> searchActiveUsers(@Param("searchTerm") String searchTerm);

    boolean existsByUsername(String username);

    boolean existsByEmail(String email);
}
