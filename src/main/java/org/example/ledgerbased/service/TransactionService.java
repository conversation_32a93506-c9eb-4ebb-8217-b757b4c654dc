package org.example.ledgerbased.service;

import org.example.ledgerbased.exception.TransactionException;
import org.example.ledgerbased.model.*;
import org.example.ledgerbased.repository.TransactionRepository;
import org.example.ledgerbased.repository.TransactionEntryRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;
import java.util.UUID;

/**
 * 交易服务类，提供财务交易的核心业务逻辑
 * 实现复式记账原理，确保每个交易的借贷平衡
 * 支持交易的完整生命周期管理：创建、审批、过账、冲销
 */
@Service
@Transactional
public class TransactionService {

    /** 交易数据访问对象 */
    private final TransactionRepository transactionRepository;

    /** 交易分录数据访问对象 */
    private final TransactionEntryRepository transactionEntryRepository;

    /** 账户服务，用于验证账户和更新余额 */
    private final AccountService accountService;

    /**
     * 构造函数，注入所需的依赖服务
     *
     * @param transactionRepository 交易数据访问对象
     * @param transactionEntryRepository 交易分录数据访问对象
     * @param accountService 账户服务
     */
    @Autowired
    public TransactionService(TransactionRepository transactionRepository,
                            TransactionEntryRepository transactionEntryRepository,
                            AccountService accountService) {
        this.transactionRepository = transactionRepository;
        this.transactionEntryRepository = transactionEntryRepository;
        this.accountService = accountService;
    }

    /**
     * 创建新的财务交易
     * 实现复式记账原理，验证借贷平衡，生成唯一交易编号
     * 交易创建后状态为PENDING，需要审批后才能过账
     *
     * @param description 交易描述
     * @param transactionDate 交易日期
     * @param entries 交易分录列表，必须至少包含2个分录
     * @param currencyId 货币ID
     * @param createdBy 创建者用户ID
     * @return 创建的交易对象
     * @throws TransactionException.UnbalancedTransactionException 如果借贷不平衡
     * @throws TransactionException 如果分录验证失败
     */
    public Transaction createTransaction(String description, LocalDate transactionDate,
                                       List<TransactionEntry> entries, Long currencyId, Long createdBy) {

        // 生成唯一交易编号
        String transactionNumber = generateTransactionNumber();

        // 验证分录
        validateTransactionEntries(entries);

        // 计算交易总金额
        BigDecimal totalAmount = calculateTotalAmount(entries);

        // 创建交易对象
        Transaction transaction = new Transaction(transactionNumber, description, transactionDate,
                                                totalAmount, currencyId, createdBy);
        transaction.setEntries(entries);

        // 验证交易借贷平衡
        if (!transaction.isBalanced()) {
            throw new TransactionException.UnbalancedTransactionException(
                "Debits and credits must be equal");
        }

        // 保存交易
        Transaction savedTransaction = transactionRepository.save(transaction);

        // 保存分录
        for (TransactionEntry entry : entries) {
            entry.setTransactionId(savedTransaction.getId());
            transactionEntryRepository.save(entry);
        }

        return savedTransaction;
    }

    /**
     * 根据交易ID获取交易详情
     * 包含完整的交易信息和所有相关的分录数据
     *
     * @param transactionId 交易ID
     * @return 完整的交易对象，包含分录列表
     * @throws TransactionException.TransactionNotFoundException 如果交易不存在
     */
    @Transactional(readOnly = true)
    public Transaction getTransactionById(Long transactionId) {
        Transaction transaction = transactionRepository.findById(transactionId)
                .orElseThrow(() -> new TransactionException.TransactionNotFoundException(transactionId));

        // 加载交易分录
        List<TransactionEntry> entries = transactionEntryRepository.findByTransactionId(transactionId);
        transaction.setEntries(entries);

        return transaction;
    }

    /**
     * 根据交易编号获取交易详情
     * 通过系统生成的唯一交易编号查找交易
     *
     * @param transactionNumber 交易编号
     * @return 完整的交易对象，包含分录列表
     * @throws TransactionException.TransactionNotFoundException 如果交易不存在
     */
    @Transactional(readOnly = true)
    public Transaction getTransactionByNumber(String transactionNumber) {
        Transaction transaction = transactionRepository.findByTransactionNumber(transactionNumber)
                .orElseThrow(() -> new TransactionException.TransactionNotFoundException(transactionNumber));

        // 加载交易分录
        List<TransactionEntry> entries = transactionEntryRepository.findByTransactionId(transaction.getId());
        transaction.setEntries(entries);

        return transaction;
    }

    /**
     * 审批交易
     * 将待审批状态的交易变更为已审批状态
     * 只有PENDING状态的交易可以被审批
     *
     * @param transactionId 交易ID
     * @param approvedBy 审批人用户ID
     * @return 审批后的交易对象
     * @throws TransactionException.InvalidTransactionStatusException 如果交易状态不允许审批
     * @throws TransactionException.TransactionNotFoundException 如果交易不存在
     */
    public Transaction approveTransaction(Long transactionId, Long approvedBy) {
        Transaction transaction = getTransactionById(transactionId);

        if (!transaction.getStatus().canBeApproved()) {
            throw new TransactionException.InvalidTransactionStatusException(
                "Transaction cannot be approved in status: " + transaction.getStatus());
        }

        transaction.approve(approvedBy);
        return transactionRepository.save(transaction);
    }

    /**
     * 过账交易
     * 将已审批的交易过账到总账，更新相关账户余额
     * 只有APPROVED状态的交易可以被过账
     * 过账后交易状态变为POSTED，开始影响账户余额
     *
     * @param transactionId 交易ID
     * @return 过账后的交易对象
     * @throws TransactionException.InvalidTransactionStatusException 如果交易状态不允许过账
     * @throws TransactionException.TransactionNotFoundException 如果交易不存在
     */
    public Transaction postTransaction(Long transactionId) {
        Transaction transaction = getTransactionById(transactionId);

        if (!transaction.getStatus().canBePosted()) {
            throw new TransactionException.InvalidTransactionStatusException(
                "Transaction cannot be posted in status: " + transaction.getStatus());
        }

        // 更新账户余额
        for (TransactionEntry entry : transaction.getEntries()) {
            accountService.updateAccountBalance(entry.getAccountId(), entry.getAmount(), entry.getEntryType());
        }

        transaction.post();
        return transactionRepository.save(transaction);
    }

    /**
     * 冲销交易
     * 创建一个与原交易相反的冲销交易来抵消原交易的影响
     * 只有POSTED状态的交易可以被冲销
     * 冲销交易会自动审批并过账，原交易状态变为REVERSED
     *
     * @param transactionId 原交易ID
     * @param reversalReason 冲销原因
     * @param reversedBy 冲销操作人用户ID
     * @return 创建的冲销交易对象
     * @throws TransactionException.InvalidTransactionStatusException 如果交易状态不允许冲销
     * @throws TransactionException.TransactionNotFoundException 如果交易不存在
     */
    public Transaction reverseTransaction(Long transactionId, String reversalReason, Long reversedBy) {
        Transaction originalTransaction = getTransactionById(transactionId);

        if (!originalTransaction.getStatus().canBeReversed()) {
            throw new TransactionException.InvalidTransactionStatusException(
                "Transaction cannot be reversed in status: " + originalTransaction.getStatus());
        }

        // 创建冲销分录（与原分录相反）
        List<TransactionEntry> reversalEntries = originalTransaction.getEntries().stream()
                .map(entry -> new TransactionEntry(
                    null, // 保存交易时会设置
                    entry.getAccountId(),
                    entry.getEntryType().opposite(),
                    entry.getAmount(),
                    "Reversal of: " + entry.getDescription()
                ))
                .toList();

        // 创建冲销交易
        Transaction reversalTransaction = createTransaction(
            "Reversal of " + originalTransaction.getTransactionNumber() + ": " + reversalReason,
            LocalDate.now(),
            reversalEntries,
            originalTransaction.getCurrencyId(),
            reversedBy
        );

        // 自动审批并过账冲销交易
        approveTransaction(reversalTransaction.getId(), reversedBy);
        postTransaction(reversalTransaction.getId());

        // 标记原交易为已冲销
        originalTransaction.reverse();
        transactionRepository.save(originalTransaction);

        return reversalTransaction;
    }

    /**
     * 根据交易状态查询交易列表
     *
     * @param status 交易状态
     * @return 指定状态的交易列表
     */
    @Transactional(readOnly = true)
    public List<Transaction> getTransactionsByStatus(TransactionStatus status) {
        return transactionRepository.findByStatus(status);
    }

    /**
     * 根据日期范围查询交易列表
     *
     * @param startDate 开始日期（包含）
     * @param endDate 结束日期（包含）
     * @return 指定日期范围内的交易列表
     */
    @Transactional(readOnly = true)
    public List<Transaction> getTransactionsByDateRange(LocalDate startDate, LocalDate endDate) {
        return transactionRepository.findByDateRange(startDate, endDate);
    }

    /**
     * 根据用户ID查询交易列表
     * 查询指定用户创建的所有交易
     *
     * @param userId 用户ID
     * @return 指定用户创建的交易列表
     */
    @Transactional(readOnly = true)
    public List<Transaction> getTransactionsByUser(Long userId) {
        return transactionRepository.findByCreatedBy(userId);
    }

    /**
     * 搜索交易
     * 根据关键词在交易描述、参考编号、交易编号中进行模糊搜索
     *
     * @param searchTerm 搜索关键词
     * @return 匹配的交易列表
     */
    @Transactional(readOnly = true)
    public List<Transaction> searchTransactions(String searchTerm) {
        return transactionRepository.searchTransactions("%" + searchTerm + "%");
    }

    /**
     * 验证交易分录
     * 确保分录符合复式记账的基本要求
     *
     * @param entries 交易分录列表
     * @throws TransactionException 如果分录验证失败
     */
    private void validateTransactionEntries(List<TransactionEntry> entries) {
        if (entries == null || entries.size() < 2) {
            throw new TransactionException("Transaction must have at least 2 entries");
        }

        for (TransactionEntry entry : entries) {
            if (entry.getAmount() == null || entry.getAmount().compareTo(BigDecimal.ZERO) <= 0) {
                throw new TransactionException("Entry amount must be positive");
            }

            if (entry.getAccountId() == null) {
                throw new TransactionException("Entry must have an account");
            }

            // 验证账户存在且处于激活状态
            Account account = accountService.getAccountById(entry.getAccountId());
            if (!account.isActive()) {
                throw new TransactionException("Cannot use inactive account: " + account.getAccountNumber());
            }
        }
    }

    /**
     * 计算交易总金额
     * 交易总金额等于所有借方分录的金额之和
     *
     * @param entries 交易分录列表
     * @return 交易总金额
     */
    private BigDecimal calculateTotalAmount(List<TransactionEntry> entries) {
        return entries.stream()
                .filter(entry -> entry.getEntryType() == EntryType.DEBIT)
                .map(TransactionEntry::getAmount)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
    }

    /**
     * 生成唯一的交易编号
     * 格式：TXN-时间戳-UUID前8位
     * 确保交易编号在系统中的唯一性
     *
     * @return 唯一的交易编号
     */
    private String generateTransactionNumber() {
        String prefix = "TXN";
        String timestamp = String.valueOf(System.currentTimeMillis());
        String uuid = UUID.randomUUID().toString().substring(0, 8).toUpperCase();
        return prefix + "-" + timestamp + "-" + uuid;
    }
}
