package org.example.ledgerbased.dto;

import org.example.ledgerbased.model.EntryType;

import jakarta.validation.constraints.DecimalMin;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import java.math.BigDecimal;

/**
 * 交易分录请求DTO
 *
 * 用于接收客户端创建交易分录的请求参数
 * 每个分录代表复式记账中的一个借方或贷方记录
 * 包含完整的验证规则确保分录数据的正确性
 *
 * 分录要求：
 * - 必须指定有效的账户ID
 * - 必须明确借贷方向（借方或贷方）
 * - 金额必须大于0
 * - 描述信息可选但有长度限制
 */
public class TransactionEntryRequest {

    /** 账户ID，指定此分录影响的账户，必填 */
    @NotNull(message = "Account ID is required")
    private Long accountId;

    /** 分录类型，借方或贷方，必填 */
    @NotNull(message = "Entry type is required")
    private EntryType entryType;

    /** 分录金额，必须大于0，必填 */
    @NotNull(message = "Amount is required")
    @DecimalMin(value = "0.01", message = "Amount must be greater than 0")
    private BigDecimal amount;

    /** 分录描述，说明此分录的业务含义，可选但不超过255字符 */
    @Size(max = 255, message = "Description cannot exceed 255 characters")
    private String description;

    /** 默认构造函数 */
    public TransactionEntryRequest() {}

    /**
     * 构造函数
     *
     * @param accountId 账户ID
     * @param entryType 分录类型（借方或贷方）
     * @param amount 分录金额
     * @param description 分录描述
     */
    public TransactionEntryRequest(Long accountId, EntryType entryType, BigDecimal amount, String description) {
        this.accountId = accountId;
        this.entryType = entryType;
        this.amount = amount;
        this.description = description;
    }

    // Getters and Setters

    /** 获取账户ID */
    public Long getAccountId() { return accountId; }

    /** 设置账户ID */
    public void setAccountId(Long accountId) { this.accountId = accountId; }

    /** 获取分录类型 */
    public EntryType getEntryType() { return entryType; }

    /** 设置分录类型 */
    public void setEntryType(EntryType entryType) { this.entryType = entryType; }

    /** 获取分录金额 */
    public BigDecimal getAmount() { return amount; }

    /** 设置分录金额 */
    public void setAmount(BigDecimal amount) { this.amount = amount; }

    /** 获取分录描述 */
    public String getDescription() { return description; }

    /** 设置分录描述 */
    public void setDescription(String description) { this.description = description; }
}
