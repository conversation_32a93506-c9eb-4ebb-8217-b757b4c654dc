package org.example.ledgerbased.dto;

import org.example.ledgerbased.model.AccountType;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Pattern;
import java.math.BigDecimal;

/**
 * 创建账户请求DTO
 * 用于接收客户端创建账户的请求参数，包含必要的验证注解
 * 支持创建根账户和子账户
 */
public class CreateAccountRequest {

    /** 账户编号，必须唯一，格式为3-20位字母数字和连字符 */
    @NotBlank(message = "Account number is required")
    @Pattern(regexp = "^[A-Z0-9-]{3,20}$", message = "Account number must be 3-20 characters, alphanumeric with hyphens")
    private String accountNumber;

    /** 账户名称，不能为空 */
    @NotBlank(message = "Account name is required")
    private String accountName;

    /** 账户类型，创建根账户时必须指定 */
    @NotNull(message = "Account type is required")
    private AccountType accountType;

    /** 父账户ID，创建子账户时指定，创建根账户时为null */
    private Long parentAccountId;

    /** 货币ID，必须指定 */
    @NotNull(message = "Currency ID is required")
    private Long currencyId;

    /** 期初余额，默认为0 */
    private BigDecimal openingBalance = BigDecimal.ZERO;

    /** 默认构造函数 */
    public CreateAccountRequest() {}

    /**
     * 构造函数
     *
     * @param accountNumber 账户编号
     * @param accountName 账户名称
     * @param accountType 账户类型
     * @param currencyId 货币ID
     * @param openingBalance 期初余额
     */
    public CreateAccountRequest(String accountNumber, String accountName, AccountType accountType,
                               Long currencyId, BigDecimal openingBalance) {
        this.accountNumber = accountNumber;
        this.accountName = accountName;
        this.accountType = accountType;
        this.currencyId = currencyId;
        this.openingBalance = openingBalance;
    }

    // Getters and Setters

    /** 获取账户编号 */
    public String getAccountNumber() { return accountNumber; }

    /** 设置账户编号 */
    public void setAccountNumber(String accountNumber) { this.accountNumber = accountNumber; }

    /** 获取账户名称 */
    public String getAccountName() { return accountName; }

    /** 设置账户名称 */
    public void setAccountName(String accountName) { this.accountName = accountName; }

    /** 获取账户类型 */
    public AccountType getAccountType() { return accountType; }

    /** 设置账户类型 */
    public void setAccountType(AccountType accountType) { this.accountType = accountType; }

    /** 获取父账户ID */
    public Long getParentAccountId() { return parentAccountId; }

    /** 设置父账户ID */
    public void setParentAccountId(Long parentAccountId) { this.parentAccountId = parentAccountId; }

    /** 获取货币ID */
    public Long getCurrencyId() { return currencyId; }

    /** 设置货币ID */
    public void setCurrencyId(Long currencyId) { this.currencyId = currencyId; }

    /** 获取期初余额 */
    public BigDecimal getOpeningBalance() { return openingBalance; }

    /** 设置期初余额 */
    public void setOpeningBalance(BigDecimal openingBalance) { this.openingBalance = openingBalance; }
}
