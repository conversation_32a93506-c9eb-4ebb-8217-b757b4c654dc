package org.example.ledgerbased.dto;

import jakarta.validation.Valid;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import java.time.LocalDate;
import java.util.List;

/**
 * 创建交易请求DTO
 *
 * 用于接收客户端创建交易的请求参数，包含完整的验证规则
 * 确保交易数据的完整性和复式记账原理的正确性
 *
 * 复式记账要求：
 * - 每个交易必须至少包含2个分录
 * - 所有借方分录金额之和必须等于所有贷方分录金额之和
 * - 每个分录必须指定明确的账户和金额
 */
public class CreateTransactionRequest {

    /** 交易描述，说明交易的业务内容，必填且不超过500字符 */
    @NotBlank(message = "Description is required")
    @Size(max = 500, message = "Description cannot exceed 500 characters")
    private String description;

    /** 参考编号，可选的外部参考号码，如发票号、合同号等 */
    private String referenceNumber;

    /** 交易日期，业务发生的日期，必填 */
    @NotNull(message = "Transaction date is required")
    private LocalDate transactionDate;

    /** 货币ID，指定交易使用的货币，必填 */
    @NotNull(message = "Currency ID is required")
    private Long currencyId;

    /**
     * 交易分录列表，实现复式记账的核心数据
     * 必须至少包含2个分录，且每个分录都要通过验证
     */
    @NotEmpty(message = "Transaction must have at least one entry")
    @Size(min = 2, message = "Transaction must have at least 2 entries for double-entry bookkeeping")
    @Valid
    private List<TransactionEntryRequest> entries;

    /** 默认构造函数 */
    public CreateTransactionRequest() {}

    /**
     * 构造函数
     *
     * @param description 交易描述
     * @param transactionDate 交易日期
     * @param currencyId 货币ID
     * @param entries 交易分录列表
     */
    public CreateTransactionRequest(String description, LocalDate transactionDate, Long currencyId,
                                   List<TransactionEntryRequest> entries) {
        this.description = description;
        this.transactionDate = transactionDate;
        this.currencyId = currencyId;
        this.entries = entries;
    }

    // Getters and Setters

    /** 获取交易描述 */
    public String getDescription() { return description; }

    /** 设置交易描述 */
    public void setDescription(String description) { this.description = description; }

    /** 获取参考编号 */
    public String getReferenceNumber() { return referenceNumber; }

    /** 设置参考编号 */
    public void setReferenceNumber(String referenceNumber) { this.referenceNumber = referenceNumber; }

    /** 获取交易日期 */
    public LocalDate getTransactionDate() { return transactionDate; }

    /** 设置交易日期 */
    public void setTransactionDate(LocalDate transactionDate) { this.transactionDate = transactionDate; }

    /** 获取货币ID */
    public Long getCurrencyId() { return currencyId; }

    /** 设置货币ID */
    public void setCurrencyId(Long currencyId) { this.currencyId = currencyId; }

    /** 获取交易分录列表 */
    public List<TransactionEntryRequest> getEntries() { return entries; }

    /** 设置交易分录列表 */
    public void setEntries(List<TransactionEntryRequest> entries) { this.entries = entries; }
}
