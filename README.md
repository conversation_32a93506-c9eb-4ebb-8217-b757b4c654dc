# Ledger-Based Transaction Management System

A comprehensive financial transaction management system built with Spring Boot, implementing double-entry bookkeeping principles for financial institutions.

## Features

### Core Functionality
- **Double-Entry Bookkeeping**: All transactions follow strict double-entry principles
- **Account Management**: Hierarchical account structure with support for multiple currencies
- **Transaction Processing**: Complete transaction lifecycle with approval workflows
- **Multi-Currency Support**: Handle transactions in different currencies with exchange rates
- **Audit Trail**: Comprehensive logging of all system operations
- **Role-Based Security**: Different access levels for different user roles

### Account Management
- Create accounts with different types (Asset, Liability, Equity, Revenue, Expense)
- Support for account hierarchies (parent-child relationships)
- Multi-currency account support
- Real-time balance calculations
- Account activation/deactivation

### Transaction Processing
- Create transactions with multiple entries
- Transaction validation (balanced entries, valid accounts)
- Approval workflow (Pending → Approved → Posted)
- Transaction reversal capabilities
- Batch transaction processing support

### Reporting & Analytics
- Account-wise ledger statements
- Trial balance reports
- Balance sheet and income statement generation
- Transaction history with filtering
- Real-time balance calculations

## Technology Stack

- **Framework**: Spring Boot 3.5.0
- **Database**: PostgreSQL with Spring Data JDBC
- **Migration**: Flyway for database versioning
- **Security**: Spring Security with role-based access control
- **Testing**: JUnit 5, Mockito, TestContainers
- **Build Tool**: Maven
- **Java Version**: 17

## Architecture

### Domain Model
```
User (Authentication & Authorization)
├── Account (Chart of Accounts)
│   ├── AccountType (ASSET, LIABILITY, EQUITY, REVENUE, EXPENSE)
│   └── Currency (Multi-currency support)
├── Transaction (Financial Transactions)
│   ├── TransactionEntry (Double-entry bookkeeping)
│   └── TransactionStatus (PENDING, APPROVED, POSTED, REVERSED)
└── AuditLog (Comprehensive audit trail)
```

### Service Layer
- **AccountService**: Account management and hierarchy operations
- **TransactionService**: Transaction processing with double-entry validation
- **UserService**: User management and authentication
- **AuditService**: Audit logging and compliance

## Getting Started

### Prerequisites
- Java 17 or higher
- PostgreSQL 12 or higher
- Maven 3.6 or higher

### Database Setup
1. Create a PostgreSQL database:
```sql
CREATE DATABASE ledger_db;
CREATE USER ledger_user WITH PASSWORD 'ledger_password';
GRANT ALL PRIVILEGES ON DATABASE ledger_db TO ledger_user;
```

2. Update `application.properties` with your database configuration:
```properties
spring.datasource.url=******************************************
spring.datasource.username=ledger_user
spring.datasource.password=ledger_password
```

### Running the Application
1. Clone the repository
2. Navigate to the project directory
3. Run the application:
```bash
mvn spring-boot:run
```

The application will start on `http://localhost:8080`

### Default Credentials
- Username: `admin`
- Password: `admin123`

## API Endpoints

### Account Management
- `POST /api/accounts` - Create new account
- `GET /api/accounts` - List all accounts
- `GET /api/accounts/{id}` - Get account by ID
- `GET /api/accounts/number/{accountNumber}` - Get account by number
- `PUT /api/accounts/{id}` - Update account
- `DELETE /api/accounts/{id}` - Deactivate account

### Transaction Management
- `POST /api/transactions` - Create new transaction
- `GET /api/transactions` - List transactions with filters
- `GET /api/transactions/{id}` - Get transaction by ID
- `POST /api/transactions/{id}/approve` - Approve transaction
- `POST /api/transactions/{id}/post` - Post transaction to ledger
- `POST /api/transactions/{id}/reverse` - Reverse transaction

### Example API Usage

#### Create Account
```bash
curl -X POST http://localhost:8080/api/accounts \
  -H "Content-Type: application/json" \
  -u admin:admin123 \
  -d '{
    "accountNumber": "1000",
    "accountName": "Cash",
    "accountType": "ASSET",
    "currencyId": 1,
    "openingBalance": 10000.00
  }'
```

#### Create Transaction
```bash
curl -X POST http://localhost:8080/api/transactions \
  -H "Content-Type: application/json" \
  -u admin:admin123 \
  -d '{
    "description": "Office supplies purchase",
    "transactionDate": "2024-01-15",
    "currencyId": 1,
    "entries": [
      {
        "accountId": 1,
        "entryType": "DEBIT",
        "amount": 500.00,
        "description": "Office supplies expense"
      },
      {
        "accountId": 2,
        "entryType": "CREDIT",
        "amount": 500.00,
        "description": "Cash payment"
      }
    ]
  }'
```

## Testing

Run the test suite:
```bash
mvn test
```

The project includes:
- Unit tests for service layer
- Integration tests for repositories
- API endpoint tests
- Test containers for database testing

## Security

The system implements:
- Role-based access control (ACCOUNTANT, APPROVER, AUDITOR, ADMIN)
- HTTP Basic authentication (configurable for JWT/OAuth2)
- Transaction approval workflows
- Comprehensive audit logging
- Data validation and sanitization

## Compliance Features

- **ACID Compliance**: All transactions are atomic, consistent, isolated, and durable
- **Immutable Audit Trail**: All operations are logged and cannot be modified
- **Double-Entry Validation**: Ensures accounting equation balance (Assets = Liabilities + Equity)
- **Maker-Checker Workflow**: Separation of transaction creation and approval
- **Data Integrity**: Cryptographic hashing for transaction verification

## Contributing

1. Fork the repository
2. Create a feature branch
3. Commit your changes
4. Push to the branch
5. Create a Pull Request

## License

This project is licensed under the MIT License - see the LICENSE file for details.
